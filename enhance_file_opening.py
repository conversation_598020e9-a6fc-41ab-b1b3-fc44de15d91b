"""
增强文件打开功能
为交互式目录添加直接文件打开和预览功能
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
import base64
import mimetypes

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def enhance_interactive_directory_with_file_opening():
    """增强交互式目录的文件打开功能"""
    print_header("📁 增强交互式目录文件打开功能")

    try:
        # 读取现有的交互式目录
        directory_path = Path("data/knowledge_base/interactive_directory.html")

        if not directory_path.exists():
            print("❌ 交互式目录文件不存在")
            return False

        # 读取知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")

        if not index_path.exists():
            print("❌ 知识库索引不存在")
            return False

        with open(index_path, 'r', encoding='utf-8') as f:
            index_data = json.load(f)

        documents = index_data.get('documents', {})

        print(f"📊 增强文档数量: {len(documents)}")

        # 生成增强版的交互式目录
        enhanced_html = await generate_enhanced_directory_html(documents)

        # 保存增强版文件
        enhanced_path = Path("data/knowledge_base/interactive_directory_enhanced.html")
        with open(enhanced_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_html)

        print(f"✅ 增强版交互式目录已创建: {enhanced_path}")

        # 同时更新原文件
        with open(directory_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_html)

        print(f"✅ 原交互式目录已更新: {directory_path}")

        return True

    except Exception as e:
        print(f"❌ 增强文件打开功能失败: {e}")
        return False

async def generate_enhanced_directory_html(documents):
    """生成增强版目录HTML"""

    # 按分类组织文档
    documents_by_category = {}
    for doc_id, doc_info in documents.items():
        category = doc_info.get('category', '其他')
        if category not in documents_by_category:
            documents_by_category[category] = []
        documents_by_category[category].append((doc_id, doc_info))

    # 统计信息
    total_docs = len(documents)
    high_importance = len([d for d in documents.values() if d.get('importance', 0) >= 7])
    categories = len(documents_by_category)
    topics = len(set(topic for d in documents.values() for topic in d.get('topics', [])))

    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata知识库交互式目录 - 增强版</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}

        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            backdrop-filter: blur(10px);
        }}

        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.8em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}

        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.3em;
            margin-bottom: 20px;
        }}

        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }}

        .stat-item {{
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            transition: transform 0.3s;
        }}

        .stat-item:hover {{
            transform: translateY(-3px);
        }}

        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }}

        .search-container {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }}

        .search-box {{
            width: 100%;
            padding: 18px 25px;
            border: 2px solid #ddd;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s;
            background: white;
        }}

        .search-box:focus {{
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.2);
        }}

        .filters {{
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }}

        .filter-btn {{
            padding: 10px 20px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }}

        .filter-btn.active {{
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }}

        .directory {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }}

        .category-section {{
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }}

        .category-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s;
        }}

        .category-header:hover {{
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }}

        .category-title {{
            font-size: 1.4em;
            font-weight: bold;
        }}

        .category-count {{
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }}

        .expand-icon {{
            transition: transform 0.3s;
            font-size: 1.3em;
        }}

        .category-content {{
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-out;
            background: #f8f9fa;
        }}

        .category-content.expanded {{
            max-height: 3000px;
        }}

        .document-item {{
            padding: 25px;
            border-bottom: 1px solid #e0e0e0;
            transition: all 0.3s;
            position: relative;
        }}

        .document-item:hover {{
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            transform: translateX(5px);
        }}

        .document-item:last-child {{
            border-bottom: none;
        }}

        .document-title {{
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
        }}

        .document-icon {{
            font-size: 1.4em;
        }}

        .document-meta {{
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }}

        .meta-item {{
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95em;
            color: #666;
        }}

        .importance-badge {{
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: bold;
        }}

        .importance-high {{
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }}

        .importance-medium {{
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }}

        .importance-low {{
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }}

        .topics {{
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }}

        .topic-tag {{
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
        }}

        .document-summary {{
            color: #555;
            font-size: 0.95em;
            line-height: 1.5;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }}

        .document-actions {{
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 15px;
        }}

        .action-btn {{
            color: white;
            text-decoration: none;
            padding: 10px 18px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            cursor: pointer;
        }}

        .action-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }}

        .btn-open {{
            background: linear-gradient(135deg, #28a745, #20c997);
        }}

        .btn-preview {{
            background: linear-gradient(135deg, #17a2b8, #138496);
        }}

        .btn-download {{
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
        }}

        .btn-json {{
            background: linear-gradient(135deg, #fd7e14, #e55a00);
        }}

        .btn-report {{
            background: linear-gradient(135deg, #6c757d, #545b62);
        }}

        .preview-modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }}

        .preview-content {{
            background: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 1000px;
            max-height: 90%;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }}

        .preview-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }}

        .preview-title {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }}

        .close-btn {{
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2em;
            transition: all 0.3s;
        }}

        .close-btn:hover {{
            background: #c0392b;
            transform: scale(1.05);
        }}

        .preview-body {{
            line-height: 1.6;
            color: #333;
        }}

        .preview-image {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }}

        .file-info {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }}

        .hidden {{
            display: none !important;
        }}

        @media (max-width: 768px) {{
            .container {{
                padding: 15px;
            }}

            .header h1 {{
                font-size: 2.2em;
            }}

            .stats {{
                grid-template-columns: repeat(2, 1fr);
            }}

            .filters {{
                justify-content: center;
            }}

            .document-meta {{
                flex-direction: column;
                gap: 10px;
            }}

            .document-actions {{
                flex-direction: column;
            }}

            .preview-content {{
                width: 95%;
                margin: 5% auto;
                padding: 20px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Ewandata知识库</h1>
            <p class="subtitle">混合AI智能知识管理系统 - 增强版交互式目录</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{total_docs}</span>
                    <span>总文档</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{categories}</span>
                    <span>分类</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{topics}</span>
                    <span>主题</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{high_importance}</span>
                    <span>高重要性</span>
                </div>
            </div>
            <p style="color: #7f8c8d; margin-top: 15px;">
                📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 🔧 增强版本 v2.0
            </p>
        </div>

        <div class="search-container">
            <input type="text" class="search-box" id="searchBox" placeholder="🔍 搜索文档、关键词、主题... (支持实时搜索)">
            <div class="filters">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="high-importance">高重要性</button>"""

    # 添加分类过滤器
    for category in sorted(documents_by_category.keys()):
        html_content += f"""
                <button class="filter-btn" data-filter="category-{category}">{category}</button>"""

    # 添加主题过滤器
    all_topics = set(topic for d in documents.values() for topic in d.get('topics', []))
    for topic in sorted(all_topics):
        html_content += f"""
                <button class="filter-btn" data-filter="topic-{topic}">{topic}</button>"""

    html_content += """
            </div>
        </div>

        <div class="directory" id="directory">"""

    # 生成每个分类的HTML
    for category, docs in sorted(documents_by_category.items(), key=lambda x: len(x[1]), reverse=True):
        # 按重要性排序文档
        docs.sort(key=lambda x: x[1].get('importance', 0), reverse=True)

        html_content += f"""
            <div class="category-section" data-category="{category}">
                <div class="category-header" onclick="toggleCategory('{category}')">
                    <div class="category-title">📁 {category}</div>
                    <div class="category-count">{len(docs)} 个文档</div>
                    <div class="expand-icon" id="icon-{category}">▼</div>
                </div>
                <div class="category-content" id="content-{category}">"""

        for doc_id, doc_info in docs:
            name = doc_info.get('name', doc_id)
            importance = doc_info.get('importance', 0)
            doc_topics = doc_info.get('topics', [])
            summary = doc_info.get('summary', '')[:200]
            original_file = doc_info.get('original_file', '')

            # 重要性样式
            if importance >= 8:
                importance_class = 'importance-high'
            elif importance >= 5:
                importance_class = 'importance-medium'
            else:
                importance_class = 'importance-low'

            # 文档图标
            file_ext = name.split('.')[-1].lower() if '.' in name else 'txt'
            if file_ext in ['py', 'js', 'html', 'css']:
                doc_icon = '💻'
            elif file_ext in ['docx', 'doc']:
                doc_icon = '📄'
            elif file_ext in ['md']:
                doc_icon = '📝'
            elif file_ext in ['txt']:
                doc_icon = '📃'
            elif file_ext in ['json']:
                doc_icon = '📊'
            elif file_ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
                doc_icon = '🖼️'
            elif file_ext in ['pdf']:
                doc_icon = '📕'
            else:
                doc_icon = '📋'

            # 确定文件路径
            if original_file:
                file_path = original_file
            else:
                # 尝试在源文件夹中找到文件
                source_folder = Path(r"C:\Users\<USER>\Desktop\临时记")
                potential_files = list(source_folder.rglob(name))
                file_path = str(potential_files[0]) if potential_files else ''

            html_content += f"""
                    <div class="document-item"
                         data-category="{category}"
                         data-importance="{importance}"
                         data-topics="{','.join(doc_topics)}"
                         data-keywords="{name.lower()} {' '.join(doc_topics).lower()} {summary.lower()}">
                        <div class="document-title">
                            <span class="document-icon">{doc_icon}</span>
                            {name}
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge {importance_class}">{importance}/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>{category}</span>
                            </div>
                            <div class="meta-item">
                                <span>📏</span>
                                <span>{doc_info.get('size_chars', 0)} 字符</span>
                            </div>
                        </div>"""

            if doc_topics:
                html_content += f"""
                        <div class="topics">"""
                for topic in doc_topics:
                    html_content += f"""
                            <span class="topic-tag">{topic}</span>"""
                html_content += """
                        </div>"""

            if summary:
                html_content += f"""
                        <div class="document-summary">{summary}...</div>"""

            # 文件操作按钮
            html_content += f"""
                        <div class="document-actions">"""

            # 打开原文件按钮
            if file_path:
                html_content += f"""
                            <button class="action-btn btn-open" onclick="openFile('{file_path.replace('\\', '\\\\')}')">
                                <span>📂</span>
                                <span>打开文件</span>
                            </button>"""

            # 预览按钮
            if file_ext in ['txt', 'md', 'py', 'js', 'html', 'css', 'json']:
                html_content += f"""
                            <button class="action-btn btn-preview" onclick="previewFile('{doc_id}', '{name}', 'text')">
                                <span>👁️</span>
                                <span>预览</span>
                            </button>"""
            elif file_ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
                html_content += f"""
                            <button class="action-btn btn-preview" onclick="previewFile('{doc_id}', '{name}', 'image')">
                                <span>🖼️</span>
                                <span>预览图片</span>
                            </button>"""

            # 下载按钮
            if file_path:
                html_content += f"""
                            <a href="file:///{file_path.replace('\\', '/')}" class="action-btn btn-download" download>
                                <span>💾</span>
                                <span>下载</span>
                            </a>"""

            # JSON数据和报告链接
            html_content += f"""
                            <a href="../processed_documents/{doc_id}.json" class="action-btn btn-json" target="_blank">
                                <span>📊</span>
                                <span>JSON数据</span>
                            </a>
                            <a href="../processed_documents/{doc_id}.md" class="action-btn btn-report" target="_blank">
                                <span>📝</span>
                                <span>详细报告</span>
                            </a>
                        </div>
                    </div>"""

        html_content += """
                </div>
            </div>"""

    # 添加预览模态框和JavaScript
    html_content += """
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="preview-modal" id="previewModal">
        <div class="preview-content">
            <div class="preview-header">
                <div class="preview-title" id="previewTitle">文件预览</div>
                <button class="close-btn" onclick="closePreview()">✕</button>
            </div>
            <div class="preview-body" id="previewBody">
                <!-- 预览内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 切换分类展开/折叠
        function toggleCategory(category) {
            const content = document.getElementById(`content-${category}`);
            const icon = document.getElementById(`icon-${category}`);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.textContent = '▼';
            } else {
                content.classList.add('expanded');
                icon.textContent = '▲';
            }
        }

        // 打开文件
        function openFile(filePath) {
            try {
                // 尝试使用不同的方法打开文件
                if (window.electronAPI) {
                    // 如果是Electron应用
                    window.electronAPI.openFile(filePath);
                } else if (navigator.userAgent.indexOf('Windows') !== -1) {
                    // Windows系统
                    window.open(`file:///${filePath.replace(/\\\\/g, '/')}`);
                } else {
                    // 其他系统或浏览器限制
                    alert(`请手动打开文件: ${filePath}`);
                }
            } catch (error) {
                console.error('打开文件失败:', error);
                alert(`无法打开文件: ${filePath}\\n请手动打开或检查文件路径`);
            }
        }

        // 预览文件
        function previewFile(docId, fileName, fileType) {
            const modal = document.getElementById('previewModal');
            const title = document.getElementById('previewTitle');
            const body = document.getElementById('previewBody');

            title.textContent = `预览: ${fileName}`;

            if (fileType === 'text') {
                // 预览文本文件
                fetch(`../processed_documents/${docId}.json`)
                    .then(response => response.json())
                    .then(data => {
                        const content = data.content || data.extracted_text || '无法获取文件内容';
                        body.innerHTML = `
                            <div class="file-info">
                                <strong>文件名:</strong> ${fileName}<br>
                                <strong>类型:</strong> 文本文件<br>
                                <strong>大小:</strong> ${content.length} 字符
                            </div>
                            <pre style="white-space: pre-wrap; background: #f8f9fa; padding: 20px; border-radius: 10px; max-height: 400px; overflow-y: auto;">${content}</pre>
                        `;
                        modal.style.display = 'block';
                    })
                    .catch(error => {
                        body.innerHTML = `
                            <div class="file-info">
                                <strong>错误:</strong> 无法加载文件内容<br>
                                <strong>详情:</strong> ${error.message}
                            </div>
                        `;
                        modal.style.display = 'block';
                    });
            } else if (fileType === 'image') {
                // 预览图片文件
                fetch(`../processed_images/${docId.replace('image_', '')}_ocr_processed.json`)
                    .then(response => response.json())
                    .then(data => {
                        const originalFile = data.original_file || '';
                        const extractedText = data.ocr_result?.extracted_text || '无OCR文本';

                        body.innerHTML = `
                            <div class="file-info">
                                <strong>文件名:</strong> ${fileName}<br>
                                <strong>类型:</strong> 图片文件<br>
                                <strong>OCR文本长度:</strong> ${extractedText.length} 字符
                            </div>
                            <div style="text-align: center; margin: 20px 0;">
                                <img src="file:///${originalFile.replace(/\\\\/g, '/')}" class="preview-image" alt="${fileName}"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div style="display: none; padding: 40px; background: #f8f9fa; border-radius: 10px; color: #666;">
                                    无法显示图片预览<br>
                                    <small>路径: ${originalFile}</small>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <h4>OCR识别文本:</h4>
                                <pre style="white-space: pre-wrap; background: #f8f9fa; padding: 15px; border-radius: 10px; max-height: 300px; overflow-y: auto;">${extractedText}</pre>
                            </div>
                        `;
                        modal.style.display = 'block';
                    })
                    .catch(error => {
                        body.innerHTML = `
                            <div class="file-info">
                                <strong>错误:</strong> 无法加载图片信息<br>
                                <strong>详情:</strong> ${error.message}
                            </div>
                        `;
                        modal.style.display = 'block';
                    });
            }
        }

        // 关闭预览
        function closePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 搜索功能
        const searchBox = document.getElementById('searchBox');
        const documentItems = document.querySelectorAll('.document-item');

        searchBox.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            documentItems.forEach(item => {
                const keywords = item.getAttribute('data-keywords');
                if (keywords.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });

            // 自动展开有匹配结果的分类
            document.querySelectorAll('.category-section').forEach(section => {
                const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                const content = section.querySelector('.category-content');
                const icon = section.querySelector('.expand-icon');

                if (visibleItems.length > 0 && searchTerm) {
                    content.classList.add('expanded');
                    icon.textContent = '▲';
                }
            });
        });

        // 过滤功能
        const filterBtns = document.querySelectorAll('.filter-btn');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                documentItems.forEach(item => {
                    let show = false;

                    if (filter === 'all') {
                        show = true;
                    } else if (filter === 'high-importance') {
                        const importance = parseInt(item.getAttribute('data-importance'));
                        show = importance >= 7;
                    } else if (filter.startsWith('category-')) {
                        const category = filter.replace('category-', '');
                        show = item.getAttribute('data-category') === category;
                    } else if (filter.startsWith('topic-')) {
                        const topic = filter.replace('topic-', '');
                        const topics = item.getAttribute('data-topics');
                        show = topics.includes(topic);
                    }

                    item.style.display = show ? 'block' : 'none';
                });

                // 自动展开有匹配结果的分类
                document.querySelectorAll('.category-section').forEach(section => {
                    const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                    const content = section.querySelector('.category-content');
                    const icon = section.querySelector('.expand-icon');

                    if (visibleItems.length > 0) {
                        content.classList.add('expanded');
                        icon.textContent = '▲';
                    } else {
                        content.classList.remove('expanded');
                        icon.textContent = '▼';
                    }
                });
            });
        });

        // 默认展开第一个分类
        document.addEventListener('DOMContentLoaded', function() {
            const firstCategory = document.querySelector('.category-section');
            if (firstCategory) {
                const categoryName = firstCategory.getAttribute('data-category');
                toggleCategory(categoryName);
            }

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closePreview();
                }
            });
        });
    </script>
</body>
</html>"""

    return html_content

async def main():
    """主函数"""
    print_header("📁 文件打开功能增强")

    print("增强文件打开功能:")
    print("1. 直接文件打开功能")
    print("2. 文件预览功能")
    print("3. 下载按钮")
    print("4. 增强的用户界面")

    success = await enhance_interactive_directory_with_file_opening()

    if success:
        print("\n🎉 文件打开功能增强完成！")
        print("✅ 直接文件打开: 支持多种文件类型")
        print("✅ 文件预览: 文本和图片预览")
        print("✅ 下载功能: 一键下载文件")
        print("✅ 增强界面: 现代化设计和交互")

        enhanced_path = Path("data/knowledge_base/interactive_directory.html").absolute()
        print(f"\n🌐 在浏览器中查看: file:///{enhanced_path}")
    else:
        print("\n❌ 文件打开功能增强失败")

    return success

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())