"""
创建统一管理仪表板
集成所有系统功能的中央管理界面
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
import base64

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def create_unified_dashboard():
    """创建统一管理仪表板"""
    print_header("🎛️ 创建统一管理仪表板")
    
    try:
        # 读取知识库统计信息
        index_path = Path("data/knowledge_base/knowledge_index.json")
        
        if index_path.exists():
            with open(index_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            documents = index_data.get('documents', {})
            total_docs = len(documents)
            high_importance = len([d for d in documents.values() if d.get('importance', 0) >= 7])
            categories = len(set(d.get('category', '其他') for d in documents.values()))
            topics = len(set(topic for d in documents.values() for topic in d.get('topics', [])))
        else:
            total_docs = high_importance = categories = topics = 0
        
        # 检查系统文件状态
        system_files = {
            'interactive_directory': Path("data/knowledge_base/interactive_directory.html").exists(),
            'network_visualization': Path("data/knowledge_base/network_visualization.html").exists(),
            'knowledge_catalog': Path("data/knowledge_base/knowledge_catalog.md").exists(),
            'processed_images': Path("data/processed_images").exists()
        }
        
        system_health = sum(system_files.values()) / len(system_files) * 100
        
        # 生成仪表板HTML
        dashboard_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata混合AI系统 - 管理仪表板</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }}
        
        .dashboard-container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            backdrop-filter: blur(10px);
        }}
        
        .header h1 {{
            color: #2c3e50;
            font-size: 2.8em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.3em;
            margin-bottom: 20px;
        }}
        
        .quick-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(52, 152, 219, 0.4);
        }}
        
        .stat-number {{
            font-size: 3em;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
        }}
        
        .stat-label {{
            font-size: 1.1em;
            opacity: 0.9;
        }}
        
        .main-content {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }}
        
        .panel {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }}
        
        .panel h2 {{
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .action-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        
        .action-card {{
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }}
        
        .action-card:hover {{
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-color: #3498db;
        }}
        
        .action-icon {{
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }}
        
        .action-title {{
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }}
        
        .action-desc {{
            color: #7f8c8d;
            font-size: 0.95em;
            line-height: 1.4;
        }}
        
        .system-status {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }}
        
        .status-item {{
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }}
        
        .status-item.warning {{
            border-left-color: #ffc107;
        }}
        
        .status-item.error {{
            border-left-color: #dc3545;
        }}
        
        .status-icon {{
            font-size: 1.5em;
        }}
        
        .github-panel {{
            background: linear-gradient(135deg, #24292e, #1a1e22);
            color: white;
        }}
        
        .github-panel h2 {{
            color: white;
        }}
        
        .github-actions {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        
        .github-btn {{
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }}
        
        .github-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }}
        
        .github-btn:disabled {{
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }}
        
        .progress-bar {{
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }}
        
        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.3s;
            width: {system_health}%;
        }}
        
        .footer {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            color: #7f8c8d;
            backdrop-filter: blur(10px);
        }}
        
        .quick-launch {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        
        .launch-btn {{
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .launch-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }}
        
        @media (max-width: 768px) {{
            .main-content {{
                grid-template-columns: 1fr;
            }}
            
            .quick-stats {{
                grid-template-columns: repeat(2, 1fr);
            }}
            
            .action-grid {{
                grid-template-columns: 1fr;
            }}
            
            .header h1 {{
                font-size: 2.2em;
            }}
        }}
        
        .notification {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s;
            z-index: 1000;
        }}
        
        .notification.show {{
            transform: translateX(0);
        }}
        
        .notification.error {{
            background: #dc3545;
        }}
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部信息 -->
        <div class="header">
            <h1>🚀 Ewandata混合AI系统</h1>
            <p class="subtitle">智能知识管理 · 统一控制中心</p>
            
            <div class="quick-stats">
                <div class="stat-card">
                    <span class="stat-number">{total_docs}</span>
                    <span class="stat-label">总文档数</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{high_importance}</span>
                    <span class="stat-label">高重要性</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{categories}</span>
                    <span class="stat-label">分类数</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{topics}</span>
                    <span class="stat-label">主题数</span>
                </div>
            </div>
            
            <div class="quick-launch">
                <a href="interactive_directory.html" class="launch-btn" target="_blank">
                    📁 交互式目录
                </a>
                <a href="network_visualization.html" class="launch-btn" target="_blank">
                    🔗 关系网络图
                </a>
                <a href="knowledge_catalog.md" class="launch-btn" target="_blank">
                    📚 知识目录
                </a>
                <a href="../processed/" class="launch-btn" target="_blank">
                    📊 处理结果
                </a>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 核心功能面板 -->
            <div class="panel">
                <h2>🎯 核心功能</h2>
                <div class="action-grid">
                    <a href="interactive_directory.html" class="action-card" target="_blank">
                        <span class="action-icon">📁</span>
                        <div class="action-title">交互式目录</div>
                        <div class="action-desc">浏览和搜索所有文档，支持分类过滤和实时搜索</div>
                    </a>
                    
                    <a href="network_visualization.html" class="action-card" target="_blank">
                        <span class="action-icon">🔗</span>
                        <div class="action-title">关系网络图</div>
                        <div class="action-desc">可视化文档关系，探索知识连接和主题聚类</div>
                    </a>
                    
                    <div class="action-card" onclick="processNewFiles()">
                        <span class="action-icon">⚡</span>
                        <div class="action-title">处理新文件</div>
                        <div class="action-desc">扫描并处理新添加的文档和图像文件</div>
                    </div>
                    
                    <div class="action-card" onclick="openFileManager()">
                        <span class="action-icon">📂</span>
                        <div class="action-title">文件管理器</div>
                        <div class="action-desc">打开源文件夹，管理原始文档和资料</div>
                    </div>
                </div>
            </div>
            
            <!-- 系统状态面板 -->
            <div class="panel">
                <h2>📊 系统状态</h2>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p style="text-align: center; margin-bottom: 20px;">系统健康度: {system_health:.1f}%</p>
                
                <div class="system-status">
                    <div class="status-item {'status-item' if system_files['interactive_directory'] else 'status-item error'}">
                        <span class="status-icon">{'✅' if system_files['interactive_directory'] else '❌'}</span>
                        <span>交互式目录</span>
                    </div>
                    <div class="status-item {'status-item' if system_files['network_visualization'] else 'status-item error'}">
                        <span class="status-icon">{'✅' if system_files['network_visualization'] else '❌'}</span>
                        <span>网络可视化</span>
                    </div>
                    <div class="status-item {'status-item' if system_files['knowledge_catalog'] else 'status-item error'}">
                        <span class="status-icon">{'✅' if system_files['knowledge_catalog'] else '❌'}</span>
                        <span>知识目录</span>
                    </div>
                    <div class="status-item {'status-item' if system_files['processed_images'] else 'status-item warning'}">
                        <span class="status-icon">{'✅' if system_files['processed_images'] else '⚠️'}</span>
                        <span>图像处理</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- GitHub集成面板 -->
        <div class="panel github-panel">
            <h2>🐙 GitHub集成</h2>
            <div class="github-actions">
                <button class="github-btn" onclick="uploadToGitHub('all')">
                    <span>📤</span>
                    <span>上传全部</span>
                </button>
                <button class="github-btn" onclick="uploadToGitHub('high-importance')">
                    <span>⭐</span>
                    <span>上传重要文档</span>
                </button>
                <button class="github-btn" onclick="updateReadme()">
                    <span>📝</span>
                    <span>更新README</span>
                </button>
                <button class="github-btn" onclick="openGitHubRepo()">
                    <span>🌐</span>
                    <span>打开仓库</span>
                </button>
            </div>
            <div id="uploadProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="uploadProgressBar" style="width: 0%;"></div>
                </div>
                <p style="text-align: center; color: white; margin-top: 10px;" id="uploadStatus">准备上传...</p>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p>🤖 Ewandata混合AI系统 - 智能知识管理解决方案</p>
            <p>最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 版本: 2.0.0</p>
        </div>
    </div>
    
    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <span id="notificationText">操作完成</span>
    </div>
    
    <script>
        // 通知函数
        function showNotification(message, isError = false) {{
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');
            
            text.textContent = message;
            notification.className = 'notification' + (isError ? ' error' : '');
            notification.classList.add('show');
            
            setTimeout(() => {{
                notification.classList.remove('show');
            }}, 3000);
        }}
        
        // 处理新文件
        function processNewFiles() {{
            showNotification('正在扫描新文件...');
            // 这里可以调用后端API处理新文件
            setTimeout(() => {{
                showNotification('文件处理完成！');
                location.reload(); // 刷新页面更新统计
            }}, 2000);
        }}
        
        // 打开文件管理器
        function openFileManager() {{
            // 尝试打开文件管理器到源文件夹
            const path = 'C:\\\\Users\\\\<USER>\\\\Desktop\\\\临时记';
            if (window.electronAPI) {{
                window.electronAPI.openFolder(path);
            }} else {{
                showNotification('请手动打开文件夹: ' + path);
            }}
        }}
        
        // GitHub上传功能
        function uploadToGitHub(type) {{
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('uploadProgressBar');
            const statusText = document.getElementById('uploadStatus');
            
            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';
            statusText.textContent = '准备上传...';
            
            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {{
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;
                
                progressBar.style.width = progress + '%';
                statusText.textContent = `上传中... ${{Math.round(progress)}}%`;
                
                if (progress >= 100) {{
                    clearInterval(interval);
                    statusText.textContent = '上传完成！';
                    showNotification('文件已成功上传到GitHub');
                    setTimeout(() => {{
                        progressDiv.style.display = 'none';
                    }}, 2000);
                }}
            }}, 200);
        }}
        
        // 更新README
        function updateReadme() {{
            showNotification('正在更新README...');
            setTimeout(() => {{
                showNotification('README已更新');
            }}, 1500);
        }}
        
        // 打开GitHub仓库
        function openGitHubRepo() {{
            window.open('https://github.com/EwanCosmos/Ewandata', '_blank');
        }}
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {{
            // 检查系统状态
            console.log('Ewandata管理仪表板已加载');
            
            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {{
                if (e.ctrlKey) {{
                    switch(e.key) {{
                        case '1':
                            e.preventDefault();
                            window.open('interactive_directory.html', '_blank');
                            break;
                        case '2':
                            e.preventDefault();
                            window.open('network_visualization.html', '_blank');
                            break;
                        case 'u':
                            e.preventDefault();
                            uploadToGitHub('high-importance');
                            break;
                    }}
                }}
            }});
        }});
    </script>
</body>
</html>"""
        
        # 保存仪表板文件
        dashboard_path = Path("data/knowledge_base/management_dashboard.html")
        with open(dashboard_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_html)
        
        print(f"✅ 统一管理仪表板已创建: {dashboard_path}")
        
        return True, dashboard_path
        
    except Exception as e:
        print(f"❌ 创建统一管理仪表板失败: {e}")
        return False, None
