"""
增强关系可视化功能
改进文档关系图谱，添加详细连接信息和交互功能
"""

import json
from pathlib import Path
from datetime import datetime

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def enhance_relationship_visualization():
    """增强关系可视化功能"""
    print_header("🔗 增强关系可视化功能")
    
    try:
        # 读取知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")
        
        if not index_path.exists():
            print("❌ 知识库索引不存在")
            return False
        
        with open(index_path, 'r', encoding='utf-8') as f:
            index_data = json.load(f)
        
        documents = index_data.get('documents', {})
        
        print(f"📊 增强关系可视化: {len(documents)} 个文档")
        
        # 生成增强版网络可视化
        enhanced_network_html = await generate_enhanced_network_visualization(documents)
        
        # 保存增强版网络可视化
        network_path = Path("data/knowledge_base/network_visualization_enhanced.html")
        with open(network_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_network_html)
        
        print(f"✅ 增强版网络可视化已创建: {network_path}")
        
        # 更新原文件
        original_path = Path("data/knowledge_base/network_visualization.html")
        with open(original_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_network_html)
        
        print(f"✅ 原网络可视化已更新: {original_path}")
        
        # 生成增强版Mermaid图
        enhanced_mermaid = await generate_enhanced_mermaid_diagram(documents)
        
        # 保存增强版Mermaid图
        mermaid_path = Path("data/knowledge_base/document_relationships_enhanced.mmd")
        with open(mermaid_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_mermaid)
        
        print(f"✅ 增强版Mermaid图已创建: {mermaid_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强关系可视化失败: {e}")
        return False

async def generate_enhanced_network_visualization(documents):
    """生成增强版网络可视化"""
    
    # 构建网络数据
    nodes = []
    links = []
    node_id_map = {}
    
    # 添加文档节点
    for i, (doc_id, doc_info) in enumerate(documents.items()):
        name = doc_info.get('name', doc_id)
        importance = doc_info.get('importance', 0)
        category = doc_info.get('category', '其他')
        topics = doc_info.get('topics', [])
        keywords = doc_info.get('keywords', [])
        
        # 节点大小基于重要性
        size = max(15, importance * 4)
        
        # 节点颜色基于分类
        color_map = {
            '技术文档': '#3498db',
            '项目管理': '#e74c3c',
            '学习笔记': '#2ecc71',
            '商业文档': '#f39c12',
            '个人资料': '#9b59b6',
            '图像文档': '#1abc9c',
            '其他': '#95a5a6'
        }
        color = color_map.get(category, '#95a5a6')
        
        node = {
            'id': i,
            'label': name[:25] + '...' if len(name) > 25 else name,
            'size': size,
            'color': color,
            'importance': importance,
            'category': category,
            'topics': topics,
            'keywords': keywords[:10],  # 限制关键词数量
            'doc_id': doc_id,
            'full_name': name,
            'summary': doc_info.get('summary', '')[:150]
        }
        
        nodes.append(node)
        node_id_map[doc_id] = i
    
    # 计算文档间的相似性并添加连接
    for i, (doc_id1, doc_info1) in enumerate(documents.items()):
        for j, (doc_id2, doc_info2) in enumerate(documents.items()):
            if i >= j:  # 避免重复连接
                continue
            
            # 计算详细相似性
            similarity_details = calculate_detailed_similarity(doc_info1, doc_info2)
            total_similarity = similarity_details['total']
            
            if total_similarity > 0.2:  # 降低阈值以显示更多连接
                link = {
                    'source': node_id_map[doc_id1],
                    'target': node_id_map[doc_id2],
                    'weight': total_similarity,
                    'width': max(1, total_similarity * 8),
                    'details': similarity_details,
                    'opacity': min(0.8, total_similarity + 0.3)
                }
                links.append(link)
    
    # 生成HTML内容
    nodes_json = json.dumps(nodes, ensure_ascii=False)
    links_json = json.dumps(links, ensure_ascii=False)
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata文档关系网络图 - 增强版</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔗</text></svg>">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }}
        
        .container {{
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            backdrop-filter: blur(10px);
        }}
        
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        
        .controls {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }}
        
        .control-row {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }}
        
        .control-btn {{
            padding: 10px 20px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }}
        
        .control-btn:hover, .control-btn.active {{
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }}
        
        .network-container {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
        }}
        
        .network-svg {{
            width: 100%;
            height: 700px;
            cursor: grab;
        }}
        
        .network-svg:active {{
            cursor: grabbing;
        }}
        
        .tooltip {{
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 13px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 350px;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }}
        
        .tooltip h4 {{
            margin-bottom: 8px;
            color: #3498db;
            border-bottom: 1px solid #3498db;
            padding-bottom: 5px;
        }}
        
        .tooltip .detail-item {{
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }}
        
        .legend {{
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #ddd;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }}
        
        .legend h4 {{
            margin-bottom: 15px;
            color: #2c3e50;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }}
        
        .legend-color {{
            width: 18px;
            height: 18px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }}
        
        .info-panel {{
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #ddd;
            max-width: 300px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }}
        
        .info-panel h4 {{
            margin-bottom: 15px;
            color: #2c3e50;
        }}
        
        .info-item {{
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
        }}
        
        .connection-details {{
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            border-left: 4px solid #3498db;
        }}
        
        .similarity-bar {{
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 5px 0;
        }}
        
        .similarity-fill {{
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 3px;
            transition: width 0.3s;
        }}
        
        @media (max-width: 768px) {{
            .container {{
                padding: 15px;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
            
            .control-row {{
                flex-direction: column;
                align-items: center;
            }}
            
            .legend, .info-panel {{
                position: relative;
                margin: 15px 0;
            }}
            
            .network-svg {{
                height: 500px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Ewandata文档关系网络图</h1>
            <p class="subtitle">增强版交互式文档关系可视化 - 详细连接分析</p>
        </div>
        
        <div class="controls">
            <div class="control-row">
                <button class="control-btn active" onclick="filterByCategory('all')">全部文档</button>
                <button class="control-btn" onclick="filterByCategory('技术文档')">技术文档</button>
                <button class="control-btn" onclick="filterByCategory('项目管理')">项目管理</button>
                <button class="control-btn" onclick="filterByCategory('个人资料')">个人资料</button>
                <button class="control-btn" onclick="filterByCategory('图像文档')">图像文档</button>
            </div>
            <div class="control-row">
                <button class="control-btn" onclick="filterByImportance(8)">高重要性 (8+)</button>
                <button class="control-btn" onclick="filterByImportance(5)">中重要性 (5+)</button>
                <button class="control-btn" onclick="showStrongConnections()">强连接</button>
                <button class="control-btn" onclick="resetZoom()">重置视图</button>
                <button class="control-btn" onclick="togglePhysics()">切换物理</button>
            </div>
        </div>
        
        <div class="network-container">
            <svg class="network-svg" id="network"></svg>
            
            <div class="legend">
                <h4>📊 图例</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>技术文档</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>项目管理</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #2ecc71;"></div>
                    <span>学习笔记</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>商业文档</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9b59b6;"></div>
                    <span>个人资料</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #1abc9c;"></div>
                    <span>图像文档</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #95a5a6;"></div>
                    <span>其他</span>
                </div>
            </div>
            
            <div class="info-panel" id="infoPanel">
                <h4>📈 网络统计</h4>
                <div class="info-item">
                    <span>节点数:</span>
                    <span id="nodeCount">{len(nodes)}</span>
                </div>
                <div class="info-item">
                    <span>连接数:</span>
                    <span id="linkCount">{len(links)}</span>
                </div>
                <div class="info-item">
                    <span>平均连接度:</span>
                    <span id="avgDegree">{len(links)*2/len(nodes):.1f}</span>
                </div>
                <p style="margin-top: 15px; font-size: 0.9em; color: #666;">
                    💡 点击节点查看详情<br>
                    🔗 悬停连接线查看关系<br>
                    🖱️ 拖拽移动节点
                </p>
            </div>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
    </div>
    
    <script>
        // 数据
        const nodes = {nodes_json};
        const links = {links_json};
        
        // SVG设置
        const svg = d3.select("#network");
        const width = 1200;
        const height = 700;
        svg.attr("viewBox", [0, 0, width, height]);
        
        // 创建缩放行为
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on("zoom", (event) => {{
                g.attr("transform", event.transform);
            }});
        
        svg.call(zoom);
        
        // 创建主容器
        const g = svg.append("g");
        
        // 物理模拟开关
        let physicsEnabled = true;
        
        // 创建力导向图
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(d => 120 - d.weight * 50))
            .force("charge", d3.forceManyBody().strength(-400))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(d => d.size + 10));
        
        // 创建连接线
        const link = g.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("stroke", "#999")
            .attr("stroke-opacity", d => d.opacity)
            .attr("stroke-width", d => d.width)
            .on("mouseover", showLinkTooltip)
            .on("mouseout", hideTooltip);
        
        // 创建节点
        const node = g.append("g")
            .selectAll("circle")
            .data(nodes)
            .enter().append("circle")
            .attr("r", d => d.size)
            .attr("fill", d => d.color)
            .attr("stroke", "#fff")
            .attr("stroke-width", 3)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended))
            .on("click", showNodeDetails)
            .on("mouseover", showNodeTooltip)
            .on("mouseout", hideTooltip);
        
        // 创建标签
        const label = g.append("g")
            .selectAll("text")
            .data(nodes)
            .enter().append("text")
            .text(d => d.label)
            .attr("font-size", "11px")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .attr("fill", "#333")
            .style("pointer-events", "none")
            .style("font-weight", "500");
        
        // 更新位置
        simulation.on("tick", () => {{
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);
            
            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);
            
            label
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        }});"""
    

        // 拖拽函数
        function dragstarted(event, d) {{
            if (!event.active && physicsEnabled) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }}

        function dragged(event, d) {{
            d.fx = event.x;
            d.fy = event.y;
        }}

        function dragended(event, d) {{
            if (!event.active && physicsEnabled) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }}

        // 显示节点详情
        function showNodeDetails(event, d) {{
            const infoPanel = document.getElementById('infoPanel');
            const connectedLinks = links.filter(l => l.source.id === d.id || l.target.id === d.id);
            const connectionCount = connectedLinks.length;

            infoPanel.innerHTML = `
                <h4>📄 文档详情</h4>
                <div class="info-item">
                    <span>名称:</span>
                    <span style="font-weight: bold;">${{d.full_name}}</span>
                </div>
                <div class="info-item">
                    <span>重要性:</span>
                    <span>${{d.importance}}/10</span>
                </div>
                <div class="info-item">
                    <span>分类:</span>
                    <span>${{d.category}}</span>
                </div>
                <div class="info-item">
                    <span>主题:</span>
                    <span>${{d.topics.join(', ') || '无'}}</span>
                </div>
                <div class="info-item">
                    <span>连接数:</span>
                    <span>${{connectionCount}}</span>
                </div>
                <div style="margin-top: 15px;">
                    <strong>摘要:</strong><br>
                    <div style="font-size: 0.9em; color: #666; margin-top: 5px; line-height: 1.4;">
                        ${{d.summary || '无摘要'}}
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>关键词:</strong><br>
                    <div style="margin-top: 5px;">
                        ${{d.keywords.map(k => `<span style="background: #e3f2fd; padding: 2px 6px; border-radius: 10px; font-size: 0.8em; margin: 2px;">${{k}}</span>`).join('')}}
                    </div>
                </div>
            `;

            // 高亮相关节点和连接
            highlightConnections(d);
        }}

        // 显示节点提示框
        function showNodeTooltip(event, d) {{
            const tooltip = document.getElementById('tooltip');
            tooltip.style.opacity = 1;
            tooltip.style.left = (event.pageX + 15) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';

            const connectedCount = links.filter(l => l.source.id === d.id || l.target.id === d.id).length;

            tooltip.innerHTML = `
                <h4>${{d.full_name}}</h4>
                <div class="detail-item">
                    <span>重要性:</span>
                    <span>${{d.importance}}/10</span>
                </div>
                <div class="detail-item">
                    <span>分类:</span>
                    <span>${{d.category}}</span>
                </div>
                <div class="detail-item">
                    <span>连接数:</span>
                    <span>${{connectedCount}}</span>
                </div>
                <div class="detail-item">
                    <span>主题:</span>
                    <span>${{d.topics.slice(0, 3).join(', ') || '无'}}</span>
                </div>
            `;
        }}

        // 显示连接提示框
        function showLinkTooltip(event, d) {{
            const tooltip = document.getElementById('tooltip');
            tooltip.style.opacity = 1;
            tooltip.style.left = (event.pageX + 15) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';

            const details = d.details;

            tooltip.innerHTML = `
                <h4>🔗 文档关系</h4>
                <div style="margin-bottom: 10px;">
                    <strong>${{d.source.label}}</strong> ↔ <strong>${{d.target.label}}</strong>
                </div>
                <div class="connection-details">
                    <div class="detail-item">
                        <span>总相似度:</span>
                        <span>${{(details.total * 100).toFixed(1)}}%</span>
                    </div>
                    <div class="similarity-bar">
                        <div class="similarity-fill" style="width: ${{details.total * 100}}%;"></div>
                    </div>
                    <div class="detail-item">
                        <span>分类相似:</span>
                        <span>${{(details.category * 100).toFixed(1)}}%</span>
                    </div>
                    <div class="detail-item">
                        <span>主题相似:</span>
                        <span>${{(details.topic * 100).toFixed(1)}}%</span>
                    </div>
                    <div class="detail-item">
                        <span>关键词相似:</span>
                        <span>${{(details.keyword * 100).toFixed(1)}}%</span>
                    </div>
                    <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                        连接强度: ${{details.total > 0.6 ? '强' : details.total > 0.3 ? '中' : '弱'}}
                    </div>
                </div>
            `;
        }}

        function hideTooltip() {{
            document.getElementById('tooltip').style.opacity = 0;
        }}

        // 高亮连接
        function highlightConnections(selectedNode) {{
            const connectedNodeIds = new Set();
            const connectedLinkIds = new Set();

            links.forEach((link, i) => {{
                if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {{
                    connectedNodeIds.add(link.source.id);
                    connectedNodeIds.add(link.target.id);
                    connectedLinkIds.add(i);
                }}
            }});

            // 更新节点样式
            node.style('opacity', d => connectedNodeIds.has(d.id) ? 1 : 0.2)
                .style('stroke-width', d => d.id === selectedNode.id ? 5 : connectedNodeIds.has(d.id) ? 3 : 1);

            // 更新连接线样式
            link.style('opacity', (d, i) => connectedLinkIds.has(i) ? 0.8 : 0.1)
                .style('stroke-width', (d, i) => connectedLinkIds.has(i) ? d.width * 1.5 : d.width);

            // 更新标签样式
            label.style('opacity', d => connectedNodeIds.has(d.id) ? 1 : 0.2);
        }}

        // 重置高亮
        function resetHighlight() {{
            node.style('opacity', 1).style('stroke-width', 3);
            link.style('opacity', d => d.opacity).style('stroke-width', d => d.width);
            label.style('opacity', 1);
        }}

        // 过滤函数
        function filterByCategory(category) {{
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            resetHighlight();

            if (category === 'all') {{
                node.style('opacity', 1);
                label.style('opacity', 1);
                link.style('opacity', d => d.opacity);
            }} else {{
                node.style('opacity', d => d.category === category ? 1 : 0.2);
                label.style('opacity', d => d.category === category ? 1 : 0.2);
                link.style('opacity', d =>
                    (d.source.category === category || d.target.category === category) ? d.opacity : 0.1
                );
            }}
        }}

        function filterByImportance(minImportance) {{
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            resetHighlight();

            node.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            label.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            link.style('opacity', d =>
                (d.source.importance >= minImportance || d.target.importance >= minImportance) ? d.opacity : 0.1
            );
        }}

        function showStrongConnections() {{
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            resetHighlight();

            const strongLinks = links.filter(l => l.weight > 0.5);
            const strongNodeIds = new Set();
            strongLinks.forEach(l => {{
                strongNodeIds.add(l.source.id);
                strongNodeIds.add(l.target.id);
            }});

            node.style('opacity', d => strongNodeIds.has(d.id) ? 1 : 0.2);
            label.style('opacity', d => strongNodeIds.has(d.id) ? 1 : 0.2);
            link.style('opacity', d => d.weight > 0.5 ? d.opacity : 0.1);
        }}

        function resetZoom() {{
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity
            );
            resetHighlight();
        }}

        function togglePhysics() {{
            physicsEnabled = !physicsEnabled;
            if (physicsEnabled) {{
                simulation.alpha(0.3).restart();
                event.target.textContent = '暂停物理';
            }} else {{
                simulation.stop();
                event.target.textContent = '启动物理';
            }}
        }}

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('增强版文档关系网络图已加载');

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {{
                switch(e.key) {{
                    case 'r':
                    case 'R':
                        resetZoom();
                        break;
                    case 'h':
                    case 'H':
                        resetHighlight();
                        break;
                    case ' ':
                        e.preventDefault();
                        togglePhysics();
                        break;
                }}
            }});

            // 双击重置
            svg.on('dblclick', resetZoom);
        }});
    </script>
</body>
</html>"""

    return html_content

def calculate_detailed_similarity(doc1, doc2):
    """计算详细的文档相似性"""
    try:
        similarities = {
            'category': 0.0,
            'topic': 0.0,
            'keyword': 0.0,
            'total': 0.0
        }

        # 分类相似性
        if doc1.get('category') == doc2.get('category'):
            similarities['category'] = 1.0

        # 主题相似性
        topics1 = set(doc1.get('topics', []))
        topics2 = set(doc2.get('topics', []))
        if topics1 and topics2:
            similarities['topic'] = len(topics1.intersection(topics2)) / len(topics1.union(topics2))

        # 关键词相似性
        keywords1 = set(doc1.get('keywords', [])[:10])
        keywords2 = set(doc2.get('keywords', [])[:10])
        if keywords1 and keywords2:
            similarities['keyword'] = len(keywords1.intersection(keywords2)) / len(keywords1.union(keywords2))

        # 计算总相似性（加权平均）
        similarities['total'] = (
            similarities['category'] * 0.3 +
            similarities['topic'] * 0.4 +
            similarities['keyword'] * 0.3
        )

        return similarities

    except Exception as e:
        return {
            'category': 0.0,
            'topic': 0.0,
            'keyword': 0.0,
            'total': 0.0
        }

async def generate_enhanced_mermaid_diagram(documents):
    """生成增强版Mermaid图"""

    # 分析文档关系
    relationships = []
    keyword_connections = {}
    topic_connections = {}
    category_connections = {}

    # 构建关系网络
    for doc_id, doc_info in documents.items():
        keywords = doc_info.get('keywords', [])
        topics = doc_info.get('topics', [])
        category = doc_info.get('category', '其他')
        importance = doc_info.get('importance', 0)

        # 关键词关系
        for keyword in keywords[:5]:
            if keyword not in keyword_connections:
                keyword_connections[keyword] = []
            keyword_connections[keyword].append((doc_id, doc_info, importance))

        # 主题关系
        for topic in topics:
            if topic not in topic_connections:
                topic_connections[topic] = []
            topic_connections[topic].append((doc_id, doc_info, importance))

        # 分类关系
        if category not in category_connections:
            category_connections[category] = []
        category_connections[category].append((doc_id, doc_info, importance))

    # 生成增强版Mermaid图表
    mermaid_content = """graph TB
    %% 增强版文档关系网络图

    %% 定义样式
    classDef highImportance fill:#ff6b6b,stroke:#d63031,stroke-width:4px,color:#fff
    classDef mediumImportance fill:#fdcb6e,stroke:#e17055,stroke-width:3px,color:#2d3436
    classDef lowImportance fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#2d3436
    classDef category fill:#6c5ce7,stroke:#5f3dc4,stroke-width:3px,color:#fff
    classDef topic fill:#00b894,stroke:#00a085,stroke-width:3px,color:#fff
    classDef keyword fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff
    classDef cluster fill:#f8f9fa,stroke:#dee2e6,stroke-width:2px,color:#495057

    %% 分类节点
"""

    # 添加分类节点
    for category, docs in category_connections.items():
        if len(docs) > 1:
            safe_category = category.replace(' ', '_').replace('/', '_').replace('-', '_')
            avg_importance = sum(doc[2] for doc in docs) / len(docs)
            mermaid_content += f'    CAT_{safe_category}["📁 {category}<br/>{len(docs)}个文档<br/>平均重要性: {avg_importance:.1f}"]\n'

    mermaid_content += "\n    %% 主题节点\n"

    # 添加主题节点
    for topic, docs in topic_connections.items():
        if len(docs) > 1:
            safe_topic = topic.replace(' ', '_').replace('/', '_').replace('-', '_')
            high_importance_count = sum(1 for doc in docs if doc[2] >= 7)
            mermaid_content += f'    TOPIC_{safe_topic}["🏷️ {topic}<br/>{len(docs)}个文档<br/>高重要性: {high_importance_count}"]\n'

    mermaid_content += "\n    %% 高重要性文档节点\n"

    # 添加高重要性文档节点
    high_importance_docs = [(doc_id, doc_info) for doc_id, doc_info in documents.items() if doc_info.get('importance', 0) >= 7]

    for doc_id, doc_info in high_importance_docs[:20]:  # 限制显示数量
        name = doc_info.get('name', doc_id)
        importance = doc_info.get('importance', 0)
        category = doc_info.get('category', '其他')
        safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_').replace('(', '_').replace(')', '_')[:25]
        short_name = name[:25] + '...' if len(name) > 25 else name

        mermaid_content += f'    DOC_{safe_name}["📄 {short_name}<br/>重要性: {importance}/10<br/>类别: {category}"]\n'

    mermaid_content += "\n    %% 关系连接\n"

    # 添加文档到分类的连接
    for category, docs in category_connections.items():
        if len(docs) > 1:
            safe_category = category.replace(' ', '_').replace('/', '_').replace('-', '_')
            for doc_id, doc_info, importance in docs:
                if importance >= 7:
                    name = doc_info.get('name', doc_id)
                    safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_').replace('(', '_').replace(')', '_')[:25]
                    mermaid_content += f'    DOC_{safe_name} --> CAT_{safe_category}\n'

    # 添加文档到主题的连接
    for topic, docs in topic_connections.items():
        if len(docs) > 1:
            safe_topic = topic.replace(' ', '_').replace('/', '_').replace('-', '_')
            for doc_id, doc_info, importance in docs:
                if importance >= 7:
                    name = doc_info.get('name', doc_id)
                    safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_').replace('(', '_').replace(')', '_')[:25]
                    mermaid_content += f'    DOC_{safe_name} --> TOPIC_{safe_topic}\n'

    # 添加主题到分类的连接
    for topic, topic_docs in topic_connections.items():
        if len(topic_docs) > 1:
            safe_topic = topic.replace(' ', '_').replace('/', '_').replace('-', '_')
            # 找到这个主题最常见的分类
            category_count = {}
            for doc_id, doc_info, importance in topic_docs:
                category = doc_info.get('category', '其他')
                category_count[category] = category_count.get(category, 0) + 1

            if category_count:
                main_category = max(category_count.items(), key=lambda x: x[1])[0]
                if category_count[main_category] > 1:
                    safe_category = main_category.replace(' ', '_').replace('/', '_').replace('-', '_')
                    mermaid_content += f'    TOPIC_{safe_topic} -.-> CAT_{safe_category}\n'

    mermaid_content += "\n    %% 应用样式\n"

    # 应用文档样式
    for doc_id, doc_info in high_importance_docs[:20]:
        name = doc_info.get('name', doc_id)
        importance = doc_info.get('importance', 0)
        safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_').replace('(', '_').replace(')', '_')[:25]

        if importance >= 9:
            mermaid_content += f'    class DOC_{safe_name} highImportance\n'
        elif importance >= 7:
            mermaid_content += f'    class DOC_{safe_name} mediumImportance\n'
        else:
            mermaid_content += f'    class DOC_{safe_name} lowImportance\n'

    # 分类和主题样式
    for category in category_connections.keys():
        if len(category_connections[category]) > 1:
            safe_category = category.replace(' ', '_').replace('/', '_').replace('-', '_')
            mermaid_content += f'    class CAT_{safe_category} category\n'

    for topic in topic_connections.keys():
        if len(topic_connections[topic]) > 1:
            safe_topic = topic.replace(' ', '_').replace('/', '_').replace('-', '_')
            mermaid_content += f'    class TOPIC_{safe_topic} topic\n'

    return mermaid_content

async def main():
    """主函数"""
    print_header("🔗 增强关系可视化功能")

    print("增强关系可视化功能:")
    print("1. 详细连接信息显示")
    print("2. 交互式节点和连接")
    print("3. 悬停提示和钻取功能")
    print("4. 增强的Mermaid图表")

    success = await enhance_relationship_visualization()

    if success:
        print("\n🎉 关系可视化功能增强完成！")
        print("✅ 详细连接分析: 显示相似性详情")
        print("✅ 交互式探索: 点击和悬停功能")
        print("✅ 高级过滤: 多维度筛选选项")
        print("✅ 增强图表: 更丰富的Mermaid图")

        network_path = Path("data/knowledge_base/network_visualization.html").absolute()
        print(f"\n🌐 在浏览器中查看: file:///{network_path}")
    else:
        print("\n❌ 关系可视化功能增强失败")

    return success

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
